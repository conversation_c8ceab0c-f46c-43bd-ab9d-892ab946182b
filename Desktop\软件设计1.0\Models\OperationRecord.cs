using System;

namespace LogRecorder.Models
{
    /// <summary>
    /// 操作记录数据模型
    /// </summary>
    public class OperationRecord
    {
        /// <summary>
        /// 软件/文件名
        /// </summary>
        public string SoftwareFileName { get; set; } = string.Empty;

        /// <summary>
        /// 时间 (日期+时分秒+毫秒)
        /// </summary>
        public DateTime Time { get; set; }

        /// <summary>
        /// 操作类型
        /// </summary>
        public string Operation { get; set; } = string.Empty;

        /// <summary>
        /// 操作分类 (键盘、鼠标、文件、程序代码等)
        /// </summary>
        public string Category { get; set; } = string.Empty;

        /// <summary>
        /// 详细描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 产品型号
        /// </summary>
        public string ProductModel { get; set; } = string.Empty;

        public OperationRecord()
        {
            Time = DateTime.Now;
        }

        public OperationRecord(string softwareFileName, string operation, string category = "", string description = "", string productModel = "")
        {
            SoftwareFileName = softwareFileName;
            Time = DateTime.Now;
            Operation = operation;
            Category = category;
            Description = description;
            ProductModel = productModel;
        }

        /// <summary>
        /// 转换为CSV行格式
        /// </summary>
        public string[] ToCsvRow()
        {
            return new string[]
            {
                SoftwareFileName,
                Time.ToString("yyyy-MM-dd HH:mm:ss.fff"),
                Operation,
                Category,
                Description
            };
        }

        /// <summary>
        /// 从CSV行创建记录
        /// </summary>
        public static OperationRecord FromCsvRow(string[] row)
        {
            if (row.Length < 5) return new OperationRecord();

            return new OperationRecord
            {
                SoftwareFileName = row[0],
                Time = DateTime.TryParse(row[1], out var time) ? time : DateTime.Now,
                Operation = row[2],
                Category = row[3],
                Description = row[4]
            };
        }
    }
}
