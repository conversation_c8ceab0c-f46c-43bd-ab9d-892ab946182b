using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using LogRecorder.Models;

namespace LogRecorder.Services
{
    /// <summary>
    /// CSV文件操作服务
    /// </summary>
    public class CsvService
    {
        private readonly string _dataDirectory;
        private static readonly string[] CsvHeaders = { "软件/文件名", "时间", "操作", "分类", "描述" };

        public CsvService(string dataDirectory = "Data")
        {
            _dataDirectory = dataDirectory;
            if (!Directory.Exists(_dataDirectory))
            {
                Directory.CreateDirectory(_dataDirectory);
            }
        }

        /// <summary>
        /// 生成记录文件名 PRec_产品型号_日期.csv
        /// </summary>
        public string GenerateFileName(string productModel, DateTime date)
        {
            string dateStr = date.ToString("yyyyMMdd");
            return $"PRec_{productModel}_{dateStr}.csv";
        }

        /// <summary>
        /// 获取完整文件路径
        /// </summary>
        public string GetFilePath(string fileName)
        {
            return Path.Combine(_dataDirectory, fileName);
        }

        /// <summary>
        /// 获取所有记录文件
        /// </summary>
        public List<string> GetAllRecordFiles()
        {
            if (!Directory.Exists(_dataDirectory))
                return new List<string>();

            return Directory.GetFiles(_dataDirectory, "PRec_*.csv")
                           .Select(Path.GetFileName)
                           .Where(f => f != null)
                           .Cast<string>()
                           .OrderByDescending(f => f)
                           .ToList();
        }

        /// <summary>
        /// 写入记录到CSV文件
        /// </summary>
        public void WriteRecords(string fileName, List<OperationRecord> records, bool append = true)
        {
            string filePath = GetFilePath(fileName);
            bool fileExists = File.Exists(filePath);

            using (var writer = new StreamWriter(filePath, append, Encoding.UTF8))
            {
                // 如果文件不存在或不是追加模式，写入标题行
                if (!fileExists || !append)
                {
                    writer.WriteLine(string.Join(",", CsvHeaders.Select(EscapeCsvField)));
                }

                // 写入数据行
                foreach (var record in records)
                {
                    var row = record.ToCsvRow();
                    writer.WriteLine(string.Join(",", row.Select(EscapeCsvField)));
                }
            }
        }

        /// <summary>
        /// 从CSV文件读取记录
        /// </summary>
        public List<OperationRecord> ReadRecords(string fileName)
        {
            string filePath = GetFilePath(fileName);
            var records = new List<OperationRecord>();

            if (!File.Exists(filePath))
                return records;

            try
            {
                var lines = File.ReadAllLines(filePath, Encoding.UTF8);
                
                // 跳过标题行
                for (int i = 1; i < lines.Length; i++)
                {
                    var fields = ParseCsvLine(lines[i]);
                    if (fields.Length >= 5)
                    {
                        records.Add(OperationRecord.FromCsvRow(fields));
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"读取文件 {fileName} 时发生错误: {ex.Message}");
            }

            return records;
        }

        /// <summary>
        /// 转义CSV字段
        /// </summary>
        private string EscapeCsvField(string field)
        {
            if (string.IsNullOrEmpty(field))
                return "";

            if (field.Contains(",") || field.Contains("\"") || field.Contains("\n") || field.Contains("\r"))
            {
                return "\"" + field.Replace("\"", "\"\"") + "\"";
            }

            return field;
        }

        /// <summary>
        /// 解析CSV行
        /// </summary>
        private string[] ParseCsvLine(string line)
        {
            var fields = new List<string>();
            bool inQuotes = false;
            var currentField = new StringBuilder();

            for (int i = 0; i < line.Length; i++)
            {
                char c = line[i];

                if (c == '"')
                {
                    if (inQuotes && i + 1 < line.Length && line[i + 1] == '"')
                    {
                        currentField.Append('"');
                        i++; // 跳过下一个引号
                    }
                    else
                    {
                        inQuotes = !inQuotes;
                    }
                }
                else if (c == ',' && !inQuotes)
                {
                    fields.Add(currentField.ToString());
                    currentField.Clear();
                }
                else
                {
                    currentField.Append(c);
                }
            }

            fields.Add(currentField.ToString());
            return fields.ToArray();
        }
    }
}
