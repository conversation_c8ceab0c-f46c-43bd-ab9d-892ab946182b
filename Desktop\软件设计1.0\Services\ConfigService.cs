using System;
using System.IO;
using Newtonsoft.Json;

namespace LogRecorder.Services
{
    /// <summary>
    /// 配置服务，用于保存和恢复界面状态
    /// </summary>
    public class ConfigService
    {
        private readonly string _configFile = "config.json";
        
        /// <summary>
        /// 应用程序配置
        /// </summary>
        public class AppConfig
        {
            public WindowConfig Window { get; set; } = new WindowConfig();
            public string LastProductModel { get; set; } = "DefaultModel";
            public string DataDirectory { get; set; } = "Data";
        }

        /// <summary>
        /// 窗口配置
        /// </summary>
        public class WindowConfig
        {
            public int Width { get; set; } = 1000;
            public int Height { get; set; } = 700;
            public int X { get; set; } = 100;
            public int Y { get; set; } = 100;
            public bool IsMaximized { get; set; } = false;
            
            // 表格列宽配置
            public int[] ColumnWidths { get; set; } = new int[] { 150, 180, 120, 100, 200 };
        }

        /// <summary>
        /// 加载配置
        /// </summary>
        public AppConfig LoadConfig()
        {
            try
            {
                if (File.Exists(_configFile))
                {
                    string json = File.ReadAllText(_configFile);
                    var config = JsonConvert.DeserializeObject<AppConfig>(json);
                    return config ?? new AppConfig();
                }
            }
            catch (Exception ex)
            {
                // 配置文件损坏时使用默认配置
                System.Diagnostics.Debug.WriteLine($"加载配置文件失败: {ex.Message}");
            }

            return new AppConfig();
        }

        /// <summary>
        /// 保存配置
        /// </summary>
        public void SaveConfig(AppConfig config)
        {
            try
            {
                string json = JsonConvert.SerializeObject(config, Formatting.Indented);
                File.WriteAllText(_configFile, json);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"保存配置文件失败: {ex.Message}");
            }
        }
    }
}
