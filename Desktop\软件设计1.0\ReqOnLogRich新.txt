功能描述：
独立的C#应用程序，用于接受主机上的操作信息，并存储为记录文件。
信息的记录使用文本实现，采用csv表格形式。
对记录软件本身的操作不进行记录。

表和数据格式：
检测信息记录文件名称为，PRec_产品型号_日期。
记录信息包括任意文件的删改添加、键盘鼠标的输入输出、软件的打开卸载更新下载等等。
检测信息记录包括以下字符串类型的字段：软件/文件名，时间，什么操作。
单个软件操作的分类包括多个字段，例如键盘，鼠标，打开的文件，打开或修改的程序代码等等。
操作的软件和日期相同的检测信息放在同一个检测信息记录文件内，如果该文件不存在，则新建。
检测信息长时间未进行任何操作进行则记录等待时间。
检测时间由日期+时分秒+毫秒组成。
记录和使用的操作根据软件名称的实际使用和打开的情况进行分类记录。


程序形式：
独立应用程序，带有用户界面。
接收到检测信息后，每收到10条信息或最后一条信息到达后超过5秒钟，则将最新信息写入文件，以减少文件操作频度。
界面上软件信息一栏，应在启动时，浏览当前存放数据的目录下操作记录的文件，将所有操作记录文件显示在下拉控件中待选。
程序启动后，自动进入侦听状态，准备接收传入的信息。
程序退出需要用户手动确认，提示信息`作业日志记录中，请确认结束记录并退出`。
程序在用户调整窗口大小和表格的列宽后，记录用户调整内容，以便在下次启动程序后按记录的参数显示。

统计功能：
软件统计功能，选定记录文件，统计特定日期区间内的操作数、修改删除数，各软件操作的数量。
记录键盘对一个软件的操作信息，具体到每个按键，同时记录鼠标对一个软件的操作信息，具体到每个动作。
可以在选择列表里选择具体的软件，可以查看对这个软件的所有操作。
优化软件卡顿报错问题。


统计内容：
1. 总体操作数和操作时间间隔：单独显示
2. 各信息：用表格显示，显示方式为该软件下的各种信息
3. 各软件的数量：用表格显示，统计不同软件分类的记录数量
5. 统计结果包括：
    - 软件统计功能，选定记录文件，统计特定日期区间内的操作数、修改删除数，各软件操作的数量。
    


统计显示方法：
`软件统计`表格显示各软件操作的信息。 


记录功能：
软件记录功能，选定记录文件，获取和显示文件内容，修改日期和时间。

