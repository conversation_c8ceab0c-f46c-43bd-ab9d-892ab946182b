功能描述：
独立的C#应用程序，用于接收并处理外部程序送达的产品检测信息，并存储为记录文件。
外部程序可以通过Socket连接到本应用程序，本应用程序负责接收、解析并存储这些消息。
信息的记录使用文本实现，采用csv表格形式。

表和数据格式：
检测信息记录文件名称为，PRec_产品型号_日期。
产品型号例如"0523","H1213","3168P"等等。
检测信息记录包括以下字符串类型的字段：流水号，检测时间，各相机判定分类。
各相机判定分类包括多个字段，例如C1，C2，C2A，C2B，C3，C4等等。
产品型号和日期相同的检测信息放在同一个检测信息记录文件内，如果该文件不存在，则新建。
判定分类取值范围从0-6。0表示异常，1表示良品分类，其他数值表示各种情形的NG类别。
检测时间由日期+时分秒+毫秒组成。
当且仅当一个检测信息记录内的各相机判定分类都是1时，才能认定为良品。

数据传输格式和要求：
Socket通信传入的信息格式为，产品型号+流水号+检测时间+各相机判定分类。
每个产品的判定分类由多个相机的结果组成，各相机判定分类这样表示：C1:1;C2:3;C3:1;C6:4; 等等。
典型的传入信息举例为，PM:0523A;SN:1005;TS:20250616-093021-233;C1:1;C2:1;C2A:1;C3:3;C4:1;C5:1;C5A:1;
程序对于传入的信息进行合规性检查，字段PM、SN、TS、C1字段必须存在，否则忽略该条信息。

程序形式：
独立应用程序，带有用户界面。
软件启动后即启动在端口12380进行侦听，接受其他程序的连接，接收传来的检测信息。
接收到检测信息后，每收到10条信息或最后一条信息到达后超过5秒钟，则将最新信息写入文件，以减少文件操作频度。
界面上产品型号一栏，应在启动时，浏览当前存放数据的目录下产品记录的文件，将所有产品型号显示在下拉控件中待选。
程序启动后，自动进入侦听状态，准备接收传入的信息。
程序退出需要用户手动确认，提示信息`作业日志记录中，请确认结束记录并退出`。
程序在用户调整窗口大小和表格的列宽后，记录用户调整内容，以便在下次启动程序后按记录的参数显示。

统计功能：
软件统计功能，选定型号，统计特定日期区间内的总体良率，各相机良率，产量，各出料分类的数量。

出料分类判定规则：
每条检测记录的出料分类按以下优先级确定：
1. 异常优先：如果任何相机判定分类为0（异常），则该条记录的出料分类为异常(0)
2. 良品判定：如果所有相机判定分类都是1，则该条记录的出料分类为良品(1)
3. 最大值原则：其他情况下，取各相机判定分类中的最大值作为出料分类

良品率计算：
只有当一条记录的出料分类为1（良品）时，才能计入良品数量。
总体良率 = 良品记录数 / 总记录数 × 100%

统计内容：
1. 总体良率和产量：单独显示
2. 各相机良率：用表格显示，计算方式为该相机判定分类为1的记录数占该相机参与检测的总记录数的百分比
3. 各出料分类的数量：用表格显示，统计不同出料分类的记录数量
4. 在统计界面内添加点击计算统计这个按钮后需要显示当时用的“表格配置参数”的信息
5. 统计结果包括：
    - 总体良率和产量
    - 各相机良率
    - 各出料分类的数量
    - 当时用的“表格配置参数”的信息

统计显示方法：
`出料分类统计`表格显示各出料分类（0-6）的记录数量，反映最终产品的质量分布情况。 

界面显示：
1. 使用饼图显示各出料分类的占比。
2. 使用柱状图显示各相机的良率。

记录功能：
软件记录功能，选定型号，获取和显示文件内容，修改日期和时间。

记录内容：
1. 参考文件fileRef\Config Parameters\品番管理,这个格文件夹内的是品番名，品番名点进去后是：表格配置参数250102，其中250102为
2025年1月2日，选择框先选择品番管理文件夹下的品番，默认是“表格配置参数”，选择完成后根据日期选择表格配置参数文件。其文件实际使用路径是D:\Config Parameters\品番管理，这个文件夹是以品番名称为命名的文件，其下是所需要的文件。
如果没有D:\Config Parameters\品番管理这个文件夹，则报错提示。
2. 需要显示表格配置参数文件内的内容，先对里面文件内容进行显示
3. 需要可以选择时间（以1个小时段统计）显示那个时间段使用的“表格配置参数”的内容

显示方式：
["外圆面伤","相机1","出口4","出口3","出口0","99999","400","0","400","0.000","良品","0","0.00%","48","1.01%","48","1.01%"],["外圆纹路","相机1","出口4","出口3","出口0","99999","400","0","400","0.000","良品","0","0.00%","22","0.46%","22","0.46%"],
这样为一个缺陷项，格式中的出口0不管，显示内容如下， 缺陷类型：外圆面上，相机：相机1，严重出口：出口4，轻微出口：出口3， 轻微上限99999，良品上限400，良品下限0，轻微下限：400。   除了这些信息，该缺陷后面的信息不管。
