# 操作记录器 (LogRecorder)

## 项目简介

操作记录器是一个独立的C#应用程序，用于接受主机上的操作信息，并存储为CSV格式的记录文件。该软件能够记录任意文件的删改添加、键盘鼠标的输入输出、软件的打开卸载更新下载等操作，并提供统计分析功能。

## 功能特性

### 核心功能
- **操作记录**: 自动记录系统操作信息，包括文件操作、键盘鼠标输入、软件操作等
- **自我过滤**: 自动过滤掉对记录软件本身的操作，避免无限循环记录
- **详细输入记录**: 记录键盘具体按键和鼠标具体动作，包括坐标信息
- **批量写入**: 每收到10条信息或超过5秒钟后自动写入文件，减少文件操作频度
- **文件管理**: 按产品型号和日期自动生成记录文件（PRec_产品型号_日期.csv格式）
- **软件筛选**: 支持按软件名称筛选查看特定软件的所有操作记录
- **统计分析**: 提供详细的操作统计，包括操作数、修改删除数、各软件操作数量等
- **界面状态保存**: 自动保存窗口大小、位置、列宽等界面参数
- **性能优化**: 异步数据加载，限制显示数量，避免界面卡顿

### 界面功能
- **记录显示**: 实时显示操作记录，支持文件选择和数据浏览
- **统计分析**: 多维度统计分析，包括概要统计、软件统计、分类统计等
- **报告导出**: 支持统计报告的文本格式导出
- **自动侦听**: 程序启动后自动进入侦听状态
- **安全退出**: 退出时确认提示，确保数据完整性

## 系统要求

- .NET 6.0 或更高版本
- Windows 操作系统
- 至少 100MB 可用磁盘空间

## 安装和运行

### 编译运行
1. 确保已安装 .NET 6.0 SDK
2. 在项目目录下打开命令行
3. 执行编译命令：
   ```bash
   dotnet build
   ```
4. 运行程序：
   ```bash
   dotnet run
   ```

### 发布部署
1. 创建发布版本：
   ```bash
   dotnet publish -c Release -r win-x64 --self-contained
   ```
2. 在 `bin/Release/net6.0-windows/win-x64/publish/` 目录下找到可执行文件

## 使用说明

### 基本操作
1. **启动程序**: 双击运行 LogRecorder.exe
2. **设置产品型号**: 在顶部输入框中设置产品型号（用于文件命名）
3. **开始记录**: 程序启动后自动开始侦听，也可手动点击"开始侦听"按钮
4. **查看记录**: 在"记录显示"选项卡中选择文件查看历史记录
5. **软件筛选**: 使用软件筛选下拉框查看特定软件的操作记录
6. **统计分析**: 在"统计分析"选项卡中进行数据分析

### 文件结构
```
Data/                          # 数据目录
├── PRec_产品型号_20231201.csv   # 记录文件
├── PRec_产品型号_20231202.csv
└── ...
config.json                    # 配置文件
```

### 记录文件格式
CSV文件包含以下字段：
- **软件/文件名**: 操作涉及的软件或文件名称
- **时间**: 操作发生的精确时间（包含毫秒）
- **操作**: 具体的操作类型（打开、关闭、修改等）
- **分类**: 操作分类（文件、键盘、鼠标、程序等）
- **描述**: 操作的详细描述
- **键盘详情**: 具体的按键信息（如果是键盘操作）
- **鼠标详情**: 具体的鼠标动作和坐标（如果是鼠标操作）
- **窗口标题**: 操作时的活动窗口标题

### 统计功能
- **概要统计**: 显示总操作数、时间跨度、平均间隔等基本信息
- **软件统计**: 按软件分类统计操作次数和占比
- **分类统计**: 按操作分类统计（键盘、鼠标、文件等）
- **操作统计**: 按操作类型统计（打开、关闭、修改等）
- **空闲分析**: 检测和显示长时间无操作的等待时间

## 配置说明

程序会自动创建 `config.json` 配置文件，包含：
- 窗口大小和位置设置
- 列宽设置
- 上次使用的产品型号
- 数据目录路径

## 注意事项

1. **数据安全**: 程序会在退出时提示确认，确保数据完整性
2. **文件权限**: 确保程序对数据目录有读写权限
3. **磁盘空间**: 长期运行需要定期清理旧的记录文件
4. **性能影响**: 程序设计为低资源占用，对系统性能影响最小

## 故障排除

### 常见问题
1. **程序无法启动**: 检查是否安装了 .NET 6.0 运行时
2. **无法写入文件**: 检查数据目录的写入权限
3. **配置丢失**: 删除 config.json 文件重新生成默认配置
4. **记录文件损坏**: 检查磁盘空间和文件权限

### 日志查看
程序运行时的错误信息会显示在状态栏中，详细错误可通过调试模式查看。

## 技术架构

### 主要组件
- **MainForm**: 主界面窗体
- **RecordingService**: 记录服务，负责数据收集和批量写入
- **CsvService**: CSV文件操作服务
- **StatisticsService**: 统计分析服务
- **ConfigService**: 配置管理服务
- **ApplicationManager**: 应用程序生命周期管理

### 设计模式
- 服务层架构
- 事件驱动模式
- 配置管理模式
- 批量处理模式

## 版本历史

### v1.0.0
- 初始版本发布
- 基本的操作记录功能
- CSV文件存储
- 统计分析功能
- 界面状态保存

## 许可证

本项目采用 MIT 许可证。

## 联系方式

如有问题或建议，请联系开发团队。
