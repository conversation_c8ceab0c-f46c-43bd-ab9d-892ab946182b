using System;
using System.Collections.Generic;
using System.Linq;
using LogRecorder.Models;

namespace LogRecorder.Services
{
    /// <summary>
    /// 统计服务
    /// </summary>
    public class StatisticsService
    {
        /// <summary>
        /// 统计结果
        /// </summary>
        public class StatisticsResult
        {
            public int TotalOperations { get; set; }
            public TimeSpan TotalTimeSpan { get; set; }
            public double AverageInterval { get; set; }
            public Dictionary<string, int> SoftwareOperationCounts { get; set; } = new Dictionary<string, int>();
            public Dictionary<string, int> CategoryCounts { get; set; } = new Dictionary<string, int>();
            public Dictionary<string, int> OperationTypeCounts { get; set; } = new Dictionary<string, int>();
            public int ModifyDeleteCount { get; set; }
            public DateTime StartTime { get; set; }
            public DateTime EndTime { get; set; }
        }

        /// <summary>
        /// 对指定日期区间内的记录进行统计
        /// </summary>
        public StatisticsResult AnalyzeRecords(List<OperationRecord> records, DateTime startDate, DateTime endDate)
        {
            // 过滤指定日期区间的记录
            var filteredRecords = records.Where(r => r.Time.Date >= startDate.Date && r.Time.Date <= endDate.Date)
                                        .OrderBy(r => r.Time)
                                        .ToList();

            var result = new StatisticsResult();

            if (!filteredRecords.Any())
                return result;

            result.TotalOperations = filteredRecords.Count;
            result.StartTime = filteredRecords.First().Time;
            result.EndTime = filteredRecords.Last().Time;
            result.TotalTimeSpan = result.EndTime - result.StartTime;

            // 计算平均时间间隔
            if (filteredRecords.Count > 1)
            {
                var intervals = new List<double>();
                for (int i = 1; i < filteredRecords.Count; i++)
                {
                    var interval = (filteredRecords[i].Time - filteredRecords[i - 1].Time).TotalSeconds;
                    intervals.Add(interval);
                }
                result.AverageInterval = intervals.Average();
            }

            // 统计各软件操作数量
            result.SoftwareOperationCounts = filteredRecords
                .GroupBy(r => r.SoftwareFileName)
                .ToDictionary(g => g.Key, g => g.Count());

            // 统计操作分类数量
            result.CategoryCounts = filteredRecords
                .Where(r => !string.IsNullOrEmpty(r.Category))
                .GroupBy(r => r.Category)
                .ToDictionary(g => g.Key, g => g.Count());

            // 统计操作类型数量
            result.OperationTypeCounts = filteredRecords
                .GroupBy(r => r.Operation)
                .ToDictionary(g => g.Key, g => g.Count());

            // 统计修改删除操作数量
            result.ModifyDeleteCount = filteredRecords
                .Count(r => r.Operation.Contains("修改") || r.Operation.Contains("删除") || 
                           r.Operation.Contains("编辑") || r.Operation.Contains("移除"));

            return result;
        }

        /// <summary>
        /// 生成统计报告文本
        /// </summary>
        public string GenerateReport(StatisticsResult result)
        {
            var report = new System.Text.StringBuilder();

            report.AppendLine("=== 操作记录统计报告 ===");
            report.AppendLine($"统计时间范围: {result.StartTime:yyyy-MM-dd HH:mm:ss} - {result.EndTime:yyyy-MM-dd HH:mm:ss}");
            report.AppendLine($"总操作数: {result.TotalOperations}");
            report.AppendLine($"总时长: {result.TotalTimeSpan.TotalHours:F2} 小时");
            report.AppendLine($"平均操作间隔: {result.AverageInterval:F2} 秒");
            report.AppendLine($"修改/删除操作数: {result.ModifyDeleteCount}");
            report.AppendLine();

            // 软件操作统计
            report.AppendLine("=== 各软件操作统计 ===");
            foreach (var item in result.SoftwareOperationCounts.OrderByDescending(x => x.Value))
            {
                report.AppendLine($"{item.Key}: {item.Value} 次");
            }
            report.AppendLine();

            // 操作分类统计
            if (result.CategoryCounts.Any())
            {
                report.AppendLine("=== 操作分类统计 ===");
                foreach (var item in result.CategoryCounts.OrderByDescending(x => x.Value))
                {
                    report.AppendLine($"{item.Key}: {item.Value} 次");
                }
                report.AppendLine();
            }

            // 操作类型统计
            report.AppendLine("=== 操作类型统计 ===");
            foreach (var item in result.OperationTypeCounts.OrderByDescending(x => x.Value))
            {
                report.AppendLine($"{item.Key}: {item.Value} 次");
            }

            return report.ToString();
        }

        /// <summary>
        /// 检测长时间无操作的等待时间
        /// </summary>
        public List<(DateTime StartTime, DateTime EndTime, TimeSpan Duration)> DetectIdlePeriods(
            List<OperationRecord> records, int idleThresholdMinutes = 5)
        {
            var idlePeriods = new List<(DateTime, DateTime, TimeSpan)>();
            var sortedRecords = records.OrderBy(r => r.Time).ToList();

            for (int i = 1; i < sortedRecords.Count; i++)
            {
                var interval = sortedRecords[i].Time - sortedRecords[i - 1].Time;
                if (interval.TotalMinutes >= idleThresholdMinutes)
                {
                    idlePeriods.Add((sortedRecords[i - 1].Time, sortedRecords[i].Time, interval));
                }
            }

            return idlePeriods;
        }
    }
}
